#!/usr/bin/env python3
"""
Advanced Discord Commands for Trading Bot Phase 3
Implements /positions, /pnl, /close, /history commands
Real-time status display and position management
"""

import logging
import discord
from discord.ext import commands, tasks
from discord import app_commands
from typing import Optional
from datetime import datetime, timezone, timedelta
import asyncio

from services.trading.position_manager import position_manager
from services.trading.trading_service import BinanceFuturesTrading
from services.trading.data_service import trading_data_service

logger = logging.getLogger(__name__)

class AdvancedTradingCommands(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.trade_channel_name = "trade"
        self.trading_service = None
        self.status_message = None
        self.status_channel_id = None

    async def cog_load(self):
        """Initialize trading service and start status update task"""
        try:
            self.trading_service = BinanceFuturesTrading()

            # Set up data service
            trading_data_service.set_trading_service(self.trading_service)
            position_manager.trading_service = self.trading_service
            position_manager.data_service = trading_data_service

            # Start data updates
            await trading_data_service.start_data_updates()

            self.update_status_message.start()
            logger.info("✅ Advanced trading commands initialized with status updates")
        except Exception as e:
            logger.error(f"❌ Failed to initialize advanced trading commands: {e}")

    async def setup_auto_status(self):
        """Setup automatic status message in trade channel"""
        try:
            # Find trade channel
            trade_channel = None
            for guild in self.bot.guilds:
                for channel in guild.text_channels:
                    if channel.name == self.trade_channel_name:
                        trade_channel = channel
                        break
                if trade_channel:
                    break

            if not trade_channel:
                logger.warning(f"⚠️ Trade channel '{self.trade_channel_name}' not found")
                return

            # Use a default user ID for status updates
            admin_user_id = "default_user"

            # Create status embed
            embed = await self._create_status_embed(admin_user_id)

            # Send and pin the message
            message = await trade_channel.send(embed=embed)
            await message.pin()

            # Store message reference
            self.status_message = message
            self.status_channel_id = trade_channel.id

            logger.info(f"✅ Auto status message created and pinned in #{trade_channel.name}")

        except Exception as e:
            logger.error(f"❌ Error setting up auto status: {e}")

    def cog_unload(self):
        """Stop status update task when cog unloads"""
        if self.update_status_message.is_running():
            self.update_status_message.cancel()

    def _check_trade_channel(self, interaction: discord.Interaction) -> bool:
        """Check if command is used in trade channel"""
        return interaction.channel.name == self.trade_channel_name

    def _normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol using centralized service"""
        from services.core.symbol_service import normalize_symbol
        return normalize_symbol(symbol)

    @tasks.loop(seconds=60)
    async def update_status_message(self):
        """Update pinned status message every 60 seconds"""
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                if not self.status_message or not self.status_channel_id:
                    return

                channel = self.bot.get_channel(self.status_channel_id)
                if not channel:
                    logger.warning(f"Status channel {self.status_channel_id} not found")
                    return

                # Use a default user ID for status updates
                admin_user_id = "default_user"

                # Create status embed
                embed = await self._create_status_embed(admin_user_id)

                # Update the pinned message
                await self.status_message.edit(embed=embed)
                return  # Success, exit the retry loop
                
            except discord.errors.ConnectionClosed as e:
                retry_count += 1
                logger.warning(f"Discord connection closed (attempt {retry_count}/{max_retries}): {e}")
                if retry_count < max_retries:
                    await asyncio.sleep(2 * retry_count)  # Exponential backoff
                else:
                    logger.error(f"❌ Failed to update status message after {max_retries} attempts: {e}")
            except discord.errors.HTTPException as e:
                retry_count += 1
                logger.warning(f"Discord HTTP error (attempt {retry_count}/{max_retries}): {e}")
                if retry_count < max_retries:
                    await asyncio.sleep(2 * retry_count)
                else:
                    logger.error(f"❌ Failed to update status message after {max_retries} attempts: {e}")
            except Exception as e:
                logger.error(f"❌ Error updating status message: {e}")
                return  # Don't retry for other types of errors

    @update_status_message.before_loop
    async def before_update_status_message(self):
        """Wait for bot to be ready before starting status updates"""
        await self.bot.wait_until_ready()

        # Setup auto status message if not already exists
        if not self.status_message:
            await asyncio.sleep(5)  # Wait a bit for bot to fully initialize
            await self.setup_auto_status()

    async def _create_status_embed(self, user_id: str) -> discord.Embed:
        """Create comprehensive status embed"""
        try:
            # Get data from data service
            summary_data = trading_data_service.get_summary_data()

            account_data = summary_data.get('account', {})
            positions_data = summary_data.get('positions', {})
            orders_data = summary_data.get('orders', {})

            # Calculate totals
            total_unrealized_pnl = positions_data.get('total_unrealized_pnl', 0)
            open_positions = positions_data.get('list', [])
            open_orders = orders_data.get('list', [])

            # Create embed
            embed = discord.Embed(
                title="📊 Trading Status Dashboard",
                color=0x00ff88 if total_unrealized_pnl >= 0 else 0xff4444,
                timestamp=datetime.now(timezone.utc)
            )

            # Account Status - Show margin balance vs wallet balance
            margin_balance = account_data.get('total_balance', 0)    # With PNL
            wallet_balance = account_data.get('wallet_balance', 0)   # Without PNL

            embed.add_field(
                name="💰 Account Status",
                value=f"Số dư margin : {margin_balance:,.2f}\n"
                      f"Số dư ví          : {wallet_balance:,.2f}",
                inline=True
            )

            # P&L Summary - Count only non-TP/SL orders for pending orders
            non_tp_sl_orders_count = 0
            for order in open_orders:
                order_type = order.get('type', '').lower()
                if 'take_profit' not in order_type and 'stop' not in order_type:
                    non_tp_sl_orders_count += 1

            pnl_color = "🟢" if total_unrealized_pnl >= 0 else "🔴"
            embed.add_field(
                name="📈 P&L Summary",
                value=f"**Unrealized P&L:** {pnl_color} ${total_unrealized_pnl:,.2f}\n"
                      f"**Open Positions:** {len(open_positions)}\n"
                      f"**Pending Orders:** {non_tp_sl_orders_count}",
                inline=True
            )

            # Position Summary - Standardized format
            if open_positions:
                position_text = ""
                for i, pos in enumerate(open_positions[:5]):  # Show max 5
                    symbol = pos.get('symbol', 'Unknown')
                    # Clean symbol format - remove /USDT:USDT suffix
                    clean_symbol = symbol.replace('/USDT:USDT', '').replace('USDT', '')

                    position_side = pos.get('position_side', 'Unknown').lower()  # Standardize to lowercase
                    entry_price = pos.get('entry_price', 0)
                    mark_price = pos.get('mark_price', 0)
                    pnl = pos.get('unrealized_pnl', 0)
                    size = pos.get('size', 0)

                    # Debug logging for position display
                    logger.info(f"Dashboard display {clean_symbol}: position_side={position_side}, pnl={pnl}, size={size}")

                    # Calculate position value
                    position_value = size * mark_price

                    pnl_emoji = "🟢" if pnl >= 0 else "🔴"

                    # Get TP/SL info and related orders
                    tp_price = pos.get('take_profit_price', 0)
                    sl_price = pos.get('stop_loss_price', 0)

                    # Removed percentage display as requested
                    position_text += f"**{clean_symbol}** {position_side.upper()} {pnl_emoji} ${pnl:,.2f}\n"
                    position_text += f"Entry: ${entry_price:,.4f} | Mark: ${mark_price:,.4f} | Value: ${position_value:,.2f} ({size:g})\n"

                    # Find TP/SL orders for this position to show values
                    tp_orders = []
                    sl_orders = []

                    for order in open_orders:
                        order_symbol = order.get('symbol', '')
                        order_type = order.get('type', '').lower()
                        order_position_side = order.get('position_side', '').upper()

                        # Match symbol and position side
                        if (order_symbol == symbol and order_position_side == position_side.upper()):
                            order_price = order.get('stop_price', 0) or order.get('trigger_price', 0) or order.get('price', 0)
                            order_amount = order.get('amount', 0)

                            if 'take_profit' in order_type:
                                tp_orders.append({'price': order_price, 'value': order_amount})
                            elif 'stop' in order_type and 'take_profit' not in order_type:
                                sl_orders.append({'price': order_price, 'value': order_amount})

                    # Display TP orders
                    if tp_orders:
                        position_text += "TP:\n"
                        for tp_order in tp_orders:
                            position_text += f"${tp_order['price']:,.2f} | Value: {tp_order['value']:g}\n"
                    else:
                        position_text += "TP: None\n"

                    # Display SL orders
                    if sl_orders:
                        position_text += "SL:\n"
                        for sl_order in sl_orders:
                            position_text += f"${sl_order['price']:,.2f} | Value: {sl_order['value']:g}\n"
                    else:
                        position_text += "SL: None\n"

                    position_text += "\n"

                if len(open_positions) > 5:
                    position_text += f"... and {len(open_positions) - 5} more"

                embed.add_field(
                    name="📊 Open Positions",
                    value=position_text or "No open positions",
                    inline=False
                )

            # Orders Summary - Only show non-TP/SL orders (new position orders)
            if open_orders:
                # Filter out TP/SL orders - only show new position orders
                non_tp_sl_orders = []
                for order in open_orders:
                    order_type = order.get('type', '').lower()
                    # Skip TP/SL orders as they are now shown with positions
                    if 'take_profit' not in order_type and 'stop' not in order_type:
                        non_tp_sl_orders.append(order)

                if non_tp_sl_orders:
                    order_text = ""
                    for i, order in enumerate(non_tp_sl_orders[:5]):  # Show max 5
                        symbol = order.get('symbol', 'Unknown')
                        # Clean symbol format - remove /USDT:USDT suffix
                        clean_symbol = symbol.replace('/USDT:USDT', '').replace('USDT', '')

                        side = order.get('side', 'Unknown')
                        order_type = order.get('type', 'Unknown')
                        position_side = order.get('position_side', '').upper()

                        price = order.get('price', 0)
                        amount = order.get('amount', 0)
                        display_price = price

                        # Calculate order value
                        order_value = amount * display_price if amount > 0 and display_price > 0 else 0

                        # Format order type and side for display
                        if side.lower() == 'buy':
                            type_display = f"{order_type.upper()} (L)"
                        elif side.lower() == 'sell':
                            type_display = f"{order_type.upper()} (S)"
                        else:
                            type_display = order_type.upper()

                        order_text += f"**{clean_symbol}** {type_display} {display_price:,.4f} | Value: ${order_value:,.2f}\n"

                    if len(non_tp_sl_orders) > 5:
                        order_text += f"... and {len(non_tp_sl_orders) - 5} more"

                    embed.add_field(
                        name="📋 Pending Orders",
                        value=order_text,
                        inline=False
                    )
                else:
                    # No non-TP/SL orders to show
                    embed.add_field(
                        name="📋 Pending Orders",
                        value="No new position orders",
                        inline=False
                    )

            embed.set_footer(text="Auto-updated every 60 seconds | Use /status to pin this message")
            return embed

        except Exception as e:
            logger.error(f"❌ Error creating status embed: {e}")
            return discord.Embed(
                title="❌ Status Error",
                description=f"Error creating status: {str(e)}",
                color=0xff4444,
                timestamp=datetime.now(timezone.utc)
            )

    @app_commands.command(name="status", description="Create/update pinned status message")
    async def create_status_message(self, interaction: discord.Interaction):
        """Create or update the pinned status message"""
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        await interaction.response.defer()

        try:
            user_id = str(interaction.user.id)

            # Check if auto status already exists
            if self.status_message and self.status_channel_id == interaction.channel.id:
                # Update existing message
                embed = await self._create_status_embed(user_id)
                await self.status_message.edit(embed=embed)
                await interaction.followup.send("✅ Status message updated!", ephemeral=True)
                logger.info(f"✅ Status message updated by {interaction.user}")
                return

            # Create new status embed
            embed = await self._create_status_embed(user_id)

            # Send the message
            message = await interaction.followup.send(embed=embed)

            # Pin the message
            await message.pin()

            # Store message reference
            self.status_message = message
            self.status_channel_id = interaction.channel.id

            logger.info(f"✅ Status message created and pinned by {interaction.user}")

        except Exception as e:
            logger.error(f"❌ Error creating status message: {e}")
            await interaction.followup.send(f"❌ Error creating status message: {str(e)}")

    @app_commands.command(name="statusinfo", description="Check auto status system information")
    async def status_info(self, interaction: discord.Interaction):
        """Check auto status system information"""
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        embed = discord.Embed(
            title="📊 Auto Status System Info",
            color=0x3498db,
            timestamp=datetime.now(timezone.utc)
        )

        # Status message info
        if self.status_message:
            embed.add_field(
                name="📌 Status Message",
                value=f"✅ Active\nMessage ID: `{self.status_message.id}`\nChannel: <#{self.status_channel_id}>",
                inline=False
            )
        else:
            embed.add_field(
                name="📌 Status Message",
                value="❌ Not active",
                inline=False
            )

        # Update task info
        if self.update_status_message.is_running():
            embed.add_field(
                name="🔄 Auto Update Task",
                value="✅ Running (60s interval)",
                inline=True
            )
        else:
            embed.add_field(
                name="🔄 Auto Update Task",
                value="❌ Not running",
                inline=True
            )

        # Trading service info
        if self.trading_service:
            embed.add_field(
                name="⚡ Trading Service",
                value="✅ Connected",
                inline=True
            )
        else:
            embed.add_field(
                name="⚡ Trading Service",
                value="❌ Not available",
                inline=True
            )

        # API Status
        try:
            if self.trading_service:
                embed.add_field(
                    name="🔗 API Status",
                    value="✅ Connected",
                    inline=True
                )
            else:
                embed.add_field(
                    name="🔗 API Status",
                    value="❌ Not Connected",
                    inline=True
                )
        except Exception as e:
            embed.add_field(
                name="🔗 API Status",
                value=f"❌ Error: {str(e)[:50]}...",
                inline=True
            )

        embed.set_footer(text="Use /status to create/update status message")
        await interaction.response.send_message(embed=embed, ephemeral=True)





    @app_commands.command(name="positions", description="View all open positions with P&L and metrics")
    @app_commands.describe(
        symbol="Filter by symbol (optional)",
        sort_by="Sort positions by: pnl, size, age, symbol"
    )
    async def view_positions(self, interaction: discord.Interaction,
                           symbol: Optional[str] = None,
                           sort_by: Optional[str] = "pnl"):
        """Display all open positions"""

        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        await interaction.response.defer()

        try:
            user_id = str(interaction.user.id)

            # Get open positions from cache
            positions = position_manager.get_positions_from_cache(symbol)

            if not positions:
                embed = discord.Embed(
                    title="📊 Open Positions",
                    description="No open positions found.",
                    color=0x95a5a6,
                    timestamp=datetime.now(timezone.utc)
                )
                await interaction.followup.send(embed=embed)
                return

            # Sort positions
            if sort_by == "pnl":
                positions.sort(key=lambda x: x.get('unrealized_pnl', 0), reverse=True)
            elif sort_by == "size":
                positions.sort(key=lambda x: abs(x.get('amount', 0)), reverse=True)
            elif sort_by == "age":
                positions.sort(key=lambda x: x.get('created_at', ''), reverse=True)
            elif sort_by == "symbol":
                positions.sort(key=lambda x: x.get('symbol', ''))

            # Calculate totals
            total_unrealized_pnl = sum(p.get('unrealized_pnl', 0) for p in positions)
            total_margin_used = sum(p.get('margin_used', 0) for p in positions)

            # Create main embed
            embed = discord.Embed(
                title="📊 Open Positions",
                color=0x00ff88 if total_unrealized_pnl >= 0 else 0xff4444,
                timestamp=datetime.now(timezone.utc)
            )

            # Add summary
            pnl_color = "🟢" if total_unrealized_pnl >= 0 else "🔴"
            embed.add_field(
                name="Trading Summary",
                value=f"**Total Unrealized P&L:** {pnl_color} ${total_unrealized_pnl:,.2f}\n"
                      f"**Total Margin Used:** ${total_margin_used:,.2f}\n"
                      f"**Open Positions:** {len(positions)}",
                inline=False
            )

            # Add individual positions (max 10 to avoid embed limits)
            for i, position in enumerate(positions[:10]):
                pnl = position.get('unrealized_pnl', 0)
                pnl_emoji = "🟢" if pnl >= 0 else "🔴"

                # Clean symbol format - remove /USDT:USDT suffix
                symbol = position.get('symbol', 'Unknown')
                clean_symbol = symbol.replace('/USDT:USDT', '').replace('USDT', '')

                # Standardize position side to lowercase
                position_side = position.get('position_side', 'N/A').lower()

                # Calculate position age
                created_at = datetime.fromisoformat(position.get('created_at', '').replace('Z', '+00:00'))
                age = datetime.now(timezone.utc) - created_at
                age_str = f"{age.days}d {age.seconds//3600}h" if age.days > 0 else f"{age.seconds//3600}h {(age.seconds%3600)//60}m"

                # Removed percentage display as requested
                position_info = (
                    f"**Side:** {position_side}\n"
                    f"**Size:** {position.get('amount', 0):.6f}\n"
                    f"**Entry:** ${position.get('entry_price', 0):,.2f}\n"
                    f"**Current:** ${position.get('current_price', 0):,.2f}\n"
                    f"**P&L:** {pnl_emoji} ${pnl:,.2f}\n"
                    f"**Age:** {age_str}"
                )

                embed.add_field(
                    name=f"{clean_symbol} #{i+1}",
                    value=position_info,
                    inline=True
                )

            if len(positions) > 10:
                embed.add_field(
                    name="Note",
                    value=f"Showing 10 of {len(positions)} positions. Use symbol filter for specific positions.",
                    inline=False
                )

            embed.set_footer(text=f"Sorted by: {sort_by} | Use /pnl for detailed P&L analysis")
            await interaction.followup.send(embed=embed)

            logger.info(f"Positions displayed for {interaction.user}: {len(positions)} positions")

        except Exception as e:
            logger.error(f"Error in positions command: {e}")
            await interaction.followup.send(f"❌ Error retrieving positions: {str(e)}")

    @app_commands.command(name="pnl", description="Comprehensive P&L analysis and performance metrics")
    @app_commands.describe(
        period="Time period: today, week, month, all",
        symbol="Filter by symbol (optional)"
    )
    async def view_pnl(self, interaction: discord.Interaction,
                      period: Optional[str] = "all",
                      symbol: Optional[str] = None):
        """Display comprehensive P&L analysis"""

        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        await interaction.response.defer()

        try:
            user_id = str(interaction.user.id)

            # Calculate time range
            now = datetime.now(timezone.utc)
            if period == "today":
                start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
            elif period == "week":
                start_time = now - timedelta(days=7)
            elif period == "month":
                start_time = now - timedelta(days=30)
            else:  # all
                start_time = None

            # Get positions from cache
            open_positions = position_manager.get_positions_from_cache(symbol)

            # For closed positions, we'll use a simplified approach since we don't have database
            closed_positions = []

            # Filter by time period if specified
            if start_time:
                closed_positions = [
                    p for p in closed_positions
                    if datetime.fromisoformat(p.get('updated_at', '').replace('Z', '+00:00')) >= start_time
                ]

            # Calculate metrics
            total_unrealized_pnl = sum(p.get('unrealized_pnl', 0) for p in open_positions)
            total_realized_pnl = sum(p.get('realized_pnl', 0) for p in closed_positions)
            total_pnl = total_unrealized_pnl + total_realized_pnl

            # Win/Loss analysis
            winning_positions = [p for p in closed_positions if p.get('realized_pnl', 0) > 0]
            losing_positions = [p for p in closed_positions if p.get('realized_pnl', 0) < 0]

            win_rate = (len(winning_positions) / len(closed_positions) * 100) if closed_positions else 0
            avg_win = sum(p.get('realized_pnl', 0) for p in winning_positions) / len(winning_positions) if winning_positions else 0
            avg_loss = sum(p.get('realized_pnl', 0) for p in losing_positions) / len(losing_positions) if losing_positions else 0

            # Create embed
            embed = discord.Embed(
                title=f"📈 P&L Analysis ({period.title()})",
                color=0x00ff88 if total_pnl >= 0 else 0xff4444,
                timestamp=datetime.now(timezone.utc)
            )

            # Overall P&L
            pnl_color = "🟢" if total_pnl >= 0 else "🔴"
            embed.add_field(
                name="💰 Overall P&L",
                value=f"**Total P&L:** {pnl_color} ${total_pnl:,.2f}\n"
                      f"**Realized:** ${total_realized_pnl:,.2f}\n"
                      f"**Unrealized:** ${total_unrealized_pnl:,.2f}",
                inline=True
            )

            # Position Statistics
            embed.add_field(
                name="📊 Position Stats",
                value=f"**Open Positions:** {len(open_positions)}\n"
                      f"**Closed Positions:** {len(closed_positions)}\n"
                      f"**Total Positions:** {len(open_positions) + len(closed_positions)}",
                inline=True
            )

            # Win/Loss Analysis
            if closed_positions:
                embed.add_field(
                    name="🎯 Win/Loss Analysis",
                    value=f"**Win Rate:** {win_rate:.1f}%\n"
                          f"**Avg Win:** ${avg_win:,.2f}\n"
                          f"**Avg Loss:** ${avg_loss:,.2f}\n"
                          f"**Profit Factor:** {abs(avg_win/avg_loss):.2f}" if avg_loss != 0 else "**Profit Factor:** ∞",
                    inline=True
                )

            # Best/Worst Performers
            if closed_positions:
                best_position = max(closed_positions, key=lambda x: x.get('realized_pnl', 0))
                worst_position = min(closed_positions, key=lambda x: x.get('realized_pnl', 0))

                embed.add_field(
                    name="🏆 Best Trade",
                    value=f"**{best_position.get('symbol')}:** ${best_position.get('realized_pnl', 0):,.2f}",
                    inline=True
                )

                embed.add_field(
                    name="💥 Worst Trade",
                    value=f"**{worst_position.get('symbol')}:** ${worst_position.get('realized_pnl', 0):,.2f}",
                    inline=True
                )

            # Symbol breakdown if no specific symbol filter
            if not symbol and open_positions:
                symbol_pnl = {}
                for position in open_positions:
                    sym = position.get('symbol', 'Unknown')
                    # Clean symbol format - remove /USDT:USDT suffix
                    clean_sym = sym.replace('/USDT:USDT', '').replace('USDT', '')
                    pnl = position.get('unrealized_pnl', 0)
                    symbol_pnl[clean_sym] = symbol_pnl.get(clean_sym, 0) + pnl

                # Top 3 symbols by P&L
                top_symbols = sorted(symbol_pnl.items(), key=lambda x: x[1], reverse=True)[:3]
                if top_symbols:
                    symbol_text = "\n".join([f"**{sym}:** ${pnl:,.2f}" for sym, pnl in top_symbols])
                    embed.add_field(
                        name="📈 Top Symbols",
                        value=symbol_text,
                        inline=True
                    )

            embed.set_footer(text="Use /history for detailed trading history")
            await interaction.followup.send(embed=embed)

            logger.info(f"P&L analysis displayed for {interaction.user}: {period} period")

        except Exception as e:
            logger.error(f"Error in pnl command: {e}")
            await interaction.followup.send(f"❌ Error calculating P&L: {str(e)}")



    @app_commands.command(name="history", description="Display trading history with performance metrics")
    @app_commands.describe(
        days="Number of days to look back (default: 30)",
        symbol="Filter by symbol (optional)",
        limit="Maximum number of trades to show (default: 20)"
    )
    async def trading_history(self, interaction: discord.Interaction,
                            days: Optional[int] = 30,
                            symbol: Optional[str] = None,
                            limit: Optional[int] = 20):
        """Display trading history"""

        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        await interaction.response.defer()

        try:
            user_id = str(interaction.user.id)

            # Calculate time range
            start_time = datetime.now(timezone.utc) - timedelta(days=days)

            # Get closed positions (completed trades)
            all_positions = position_manager.get_user_positions(user_id, symbol=symbol)
            closed_positions = [
                p for p in all_positions
                if p['status'] == 'closed' and
                datetime.fromisoformat(p.get('updated_at', '').replace('Z', '+00:00')) >= start_time
            ]

            # Sort by close time (most recent first)
            closed_positions.sort(
                key=lambda x: datetime.fromisoformat(x.get('updated_at', '').replace('Z', '+00:00')),
                reverse=True
            )

            if not closed_positions:
                embed = discord.Embed(
                    title="📜 Trading History",
                    description=f"No completed trades found in the last {days} days.",
                    color=0x95a5a6,
                    timestamp=datetime.now(timezone.utc)
                )
                await interaction.followup.send(embed=embed)
                return

            # Calculate summary metrics
            total_trades = len(closed_positions)
            total_pnl = sum(p.get('realized_pnl', 0) for p in closed_positions)
            winning_trades = [p for p in closed_positions if p.get('realized_pnl', 0) > 0]
            losing_trades = [p for p in closed_positions if p.get('realized_pnl', 0) < 0]

            win_rate = (len(winning_trades) / total_trades * 100) if total_trades > 0 else 0
            avg_win = sum(p.get('realized_pnl', 0) for p in winning_trades) / len(winning_trades) if winning_trades else 0
            avg_loss = sum(p.get('realized_pnl', 0) for p in losing_trades) / len(losing_trades) if losing_trades else 0

            # Create main embed
            embed = discord.Embed(
                title=f"📜 Trading History ({days} days)",
                color=0x00ff88 if total_pnl >= 0 else 0xff4444,
                timestamp=datetime.now(timezone.utc)
            )

            # Summary statistics
            pnl_color = "🟢" if total_pnl >= 0 else "🔴"
            embed.add_field(
                name="📊 Summary",
                value=f"**Total Trades:** {total_trades}\n"
                      f"**Total P&L:** {pnl_color} ${total_pnl:,.2f}\n"
                      f"**Win Rate:** {win_rate:.1f}%\n"
                      f"**Avg Win:** ${avg_win:,.2f}\n"
                      f"**Avg Loss:** ${avg_loss:,.2f}",
                inline=True
            )

            # Recent trades (limited to avoid embed size limits)
            recent_trades = closed_positions[:min(limit, 10)]

            for i, position in enumerate(recent_trades):
                pnl = position.get('realized_pnl', 0)
                pnl_emoji = "🟢" if pnl >= 0 else "🔴"

                # Clean symbol format - remove /USDT:USDT suffix
                symbol = position.get('symbol', 'Unknown')
                clean_symbol = symbol.replace('/USDT:USDT', '').replace('USDT', '')

                # Standardize position side to lowercase
                position_side = position.get('position_side', 'N/A').lower()

                # Calculate holding time
                created_at = datetime.fromisoformat(position.get('created_at', '').replace('Z', '+00:00'))
                updated_at = datetime.fromisoformat(position.get('updated_at', '').replace('Z', '+00:00'))
                holding_time = updated_at - created_at

                if holding_time.days > 0:
                    time_str = f"{holding_time.days}d {holding_time.seconds//3600}h"
                else:
                    time_str = f"{holding_time.seconds//3600}h {(holding_time.seconds%3600)//60}m"

                trade_info = (
                    f"**{position_side}** {position.get('amount', 0):.6f}\n"
                    f"**P&L:** {pnl_emoji} ${pnl:,.2f}\n"
                    f"**Time:** {time_str}\n"
                    f"**Closed:** {updated_at.strftime('%m/%d %H:%M')}"
                )

                embed.add_field(
                    name=f"{clean_symbol} #{i+1}",
                    value=trade_info,
                    inline=True
                )

            if len(closed_positions) > 10:
                embed.add_field(
                    name="📋 Note",
                    value=f"Showing {len(recent_trades)} of {len(closed_positions)} trades. "
                          f"Use filters to narrow results.",
                    inline=False
                )

            embed.set_footer(text=f"Use /pnl for detailed P&L analysis")
            await interaction.followup.send(embed=embed)

            logger.info(f"Trading history displayed for {interaction.user}: {len(closed_positions)} trades")

        except Exception as e:
            logger.error(f"Error in history command: {e}")
            await interaction.followup.send(f"❌ Error retrieving history: {str(e)}")

async def setup(bot):
    await bot.add_cog(AdvancedTradingCommands(bot))
